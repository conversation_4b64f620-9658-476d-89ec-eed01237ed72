import google from 'googleapis'
import { GoogleAuth } from 'google-auth-library';
import env from '#config/env/env.js'
import path from 'path'

class GoogleLib {
    static makeAuth(): google.Common.GoogleAuth {
        return new google.Auth.GoogleAuth({
            keyFile: path.resolve(import.meta.dirname, env.GOOGLE_KEY_JSON_PATH),
            scopes: ['https://www.googleapis.com/auth/spreadsheets'],
        })
    }

    static async initSheet(auth: google.Common.GoogleAuth) {
        return new google.sheets_v4.Resource$Spreadsheets({
            google: {
                _options: {
                    auth: auth
                }
            }
        });
    }

    static writeToSheet() {

    }
}

export default GoogleLib
