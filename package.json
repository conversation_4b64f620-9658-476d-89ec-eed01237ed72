{"name": "btlz-test", "version": "1.0.0", "description": "your description", "main": "app.js", "type": "module", "scripts": {"dev": "nodemon --watch src --ext ts --exec tsx src/app.ts", "watch:build": "tsc --build --force --verbose --watch", "knex:dev": "tsx src/utils/knex.ts", "build": "tsc --allowJs", "start": "node dist/app.js", "knex": "node dist/utils/knex.js", "tsc:check": "tsc --allowJs --noEmit", "prettier": "prettier --config .prettierrc.json src/**/*.js --check", "prettier-format": "prettier --config .prettierrc.json src/**/*.js --write", "eslint": "eslint", "eslint-fix": "eslint --fix"}, "imports": {"#*": ["./dist/*", "./src/*"]}, "_moduleAliases": {"#": "src"}, "author": "lucard17", "license": "ISC", "dependencies": {"commander": "^13.1.0", "dotenv": "^16.3.1", "googleapis": "^144.0.0", "knex": "^3.0.1", "log4js": "^6.9.1", "node-cron": "^4.2.1", "pg": "^8.11.3", "zod": "^3.23.8"}, "devDependencies": {"@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@babel/preset-typescript": "^7.24.7", "@types/axios": "^0.14.0", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/dateformat": "^5.0.2", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.8", "@types/knex": "^0.16.1", "@types/pg": "^8.11.10", "@types/uuid": "^10.0.0", "babel-jest": "^29.7.0", "eslint": "^9.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jsdoc": "^50.3.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-unused-imports": "^4.1.4", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.3.3", "prettier-plugin-jsdoc": "^1.3.0", "typescript": "^5.7.3"}}